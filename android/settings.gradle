pluginManagement { includeBuild("../node_modules/@react-native/gradle-plugin") }
plugins { id("com.facebook.react.settings") }
extensions.configure(com.facebook.react.ReactSettingsExtension){ ex -> ex.autolinkLibrariesFromCommand() }
rootProject.name = 'AudioApp'
include ':app'
includeBuild('../node_modules/@react-native/gradle-plugin')
include(':react-native-nitro-modules')
project(':react-native-nitro-modules').projectDir = file('../node_modules/react-native-nitro-modules/android')
